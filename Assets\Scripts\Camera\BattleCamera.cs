using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Battle;

namespace TacticalCombatSystem.Camera
{
    public class BattleCamera : MonoBehaviour
    {
        [Header("References")]
        public UnityEngine.Camera mainCamera;
        public Transform[] targetTransforms;
        
        [Header("Settings")]
        public float minOrthoSize = 5f;
        public float maxOrthoSize = 15f;
        public float padding = 2f;
        public float smoothTime = 0.3f;
        
        private Vector3 velocity = Vector3.zero;
        private float targetOrthoSize;
        private Bounds viewBounds;
        
        private void Start()
        {
            mainCamera ??= UnityEngine.Camera.main;
            if (mainCamera == null)
                mainCamera = GetComponent<UnityEngine.Camera>();

            // Set initial ortho size
            if (mainCamera != null && mainCamera.orthographic)
                targetOrthoSize = mainCamera.orthographicSize;
        }
        
        private void LateUpdate()
        {
            if (targetTransforms == null || targetTransforms.Length == 0) return;

            // Calculate bounds that encapsulate all targets
            CalculateViewBounds();

            // Update camera position
            Vector3 targetPosition = viewBounds.center;
            targetPosition.z = transform.position.z; // Maintain camera's z position

            // Smoothly move the camera
            transform.position = Vector3.SmoothDamp(
                transform.position,
                targetPosition,
                ref velocity,
                smoothTime);

            // Calculate required orthographic size
            float requiredOrthoSize = Mathf.Max(
                viewBounds.size.x / (2f * mainCamera.aspect),
                viewBounds.size.y / 2f) + padding;

            // Clamp ortho size
            requiredOrthoSize = Mathf.Clamp(requiredOrthoSize, minOrthoSize, maxOrthoSize);

            // Smoothly adjust orthographic size
            targetOrthoSize = Mathf.Lerp(
                targetOrthoSize,
                requiredOrthoSize,
                Time.deltaTime * smoothTime * 2f);

            if (mainCamera != null && mainCamera.orthographic)
                mainCamera.orthographicSize = targetOrthoSize;
        }
        
        private void CalculateViewBounds()
        {
            viewBounds = new Bounds();
            bool hasBounds = false;

            // Include all active targets in the bounds
            foreach (var target in targetTransforms)
            {
                if (target != null && target.gameObject.activeInHierarchy)
                {
                    if (!hasBounds)
                    {
                        viewBounds = new Bounds(target.position, Vector3.zero);
                        hasBounds = true;
                    }
                    else
                    {
                        viewBounds.Encapsulate(target.position);
                    }
                }
            }

            // If no valid targets, use a default bounds around the origin
            if (!hasBounds)
            {
                viewBounds = new Bounds(Vector3.zero, Vector3.one * 10f);
            }
        }
        
        // Add a target to the camera's focus
        public void AddTarget(Transform target, float weight = 1f, float radius = 1f)
        {
            if (target == null) return;

            // Add to the array if not already present
            var targetList = new List<Transform>(targetTransforms ?? new Transform[0]);
            if (!targetList.Contains(target))
            {
                targetList.Add(target);
                targetTransforms = targetList.ToArray();
            }
        }
        
        // Remove a target from the camera's focus
        public void RemoveTarget(Transform target)
        {
            if (target == null || targetTransforms == null) return;

            var targetList = new List<Transform>(targetTransforms);
            if (targetList.Remove(target))
            {
                targetTransforms = targetList.ToArray();
            }
        }
        
        // Set the camera to focus on a specific target
        public void SetFocus(Transform target, float duration = 1f)
        {
            if (target != null)
            {
                // Clear existing targets and set only the new target
                targetTransforms = new Transform[] { target };

                // Optionally, you could add a coroutine here to return to normal view after duration
                if (duration > 0)
                {
                    StartCoroutine(ReturnToNormalView(duration));
                }
            }
        }
        
        private IEnumerator ReturnToNormalView(float delay)
        {
            yield return new WaitForSeconds(delay);

            // Re-add all characters to the target group
            var battleManagerInterface = BattleManager.Instance;
            if (battleManagerInterface != null)
            {
                // Add all active characters from both teams
                var allCharacters = new List<ICombatParticipant>();
                allCharacters.AddRange(battleManagerInterface.GetPlayerTeam());
                allCharacters.AddRange(battleManagerInterface.GetEnemyTeam());

                var targetList = new List<Transform>();
                foreach (var character in allCharacters)
                {
                    var visual = FindCharacterVisual(character);
                    if (visual != null)
                    {
                        targetList.Add(visual.transform);
                    }
                }

                targetTransforms = targetList.ToArray();
            }
        }
        
        private CharacterVisual FindCharacterVisual(ICombatParticipant character)
        {
            // This is a simple implementation - you might want to maintain a dictionary
            // in the BattleManager to map characters to their visual representations
            var visuals = FindObjectsByType<CharacterVisual>(FindObjectsSortMode.None);
            foreach (var visual in visuals)
            {
                if (visual.BaseCharacter != null && visual.BaseCharacter.Equals(character))
                {
                    return visual;
                }
            }
            return null;
        }
    }
}
