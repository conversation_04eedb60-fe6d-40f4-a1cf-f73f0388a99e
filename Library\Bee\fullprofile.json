{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14000, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14000, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14000, "tid": 105, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14000, "tid": 105, "ts": 1750357599577691, "dur": 705, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14000, "tid": 105, "ts": 1750357599579232, "dur": 30, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14000, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14000, "tid": 1, "ts": 1750357599056176, "dur": 9971, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14000, "tid": 1, "ts": 1750357599066154, "dur": 67368, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14000, "tid": 1, "ts": 1750357599133525, "dur": 58743, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14000, "tid": 105, "ts": 1750357599579265, "dur": 50, "ph": "X", "name": "", "args": {}}, {"pid": 14000, "tid": 107374182400, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599055779, "dur": 36806, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599092589, "dur": 474507, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599092624, "dur": 308, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599092938, "dur": 440, "ph": "X", "name": "ProcessMessages 13318", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599093383, "dur": 128, "ph": "X", "name": "ReadAsync 13318", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599093515, "dur": 1, "ph": "X", "name": "ProcessMessages 39", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599093533, "dur": 287, "ph": "X", "name": "ReadAsync 39", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599093837, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599093840, "dur": 94, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599093938, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599093941, "dur": 179, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094124, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094126, "dur": 45, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094174, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094176, "dur": 48, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094254, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094258, "dur": 105, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094366, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094369, "dur": 44, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094416, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094418, "dur": 34, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094456, "dur": 370, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094830, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094832, "dur": 45, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094880, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094882, "dur": 67, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094952, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599094955, "dur": 43, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095000, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095003, "dur": 341, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095349, "dur": 360, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095715, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095717, "dur": 79, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095800, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095802, "dur": 72, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095883, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599095885, "dur": 560, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599096449, "dur": 2, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599096452, "dur": 392, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599096848, "dur": 1, "ph": "X", "name": "ProcessMessages 150", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599096850, "dur": 51, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599096904, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599096910, "dur": 65, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599096980, "dur": 39, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097067, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097069, "dur": 49, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097127, "dur": 1, "ph": "X", "name": "ProcessMessages 1530", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097130, "dur": 48, "ph": "X", "name": "ReadAsync 1530", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097181, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097183, "dur": 38, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097224, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097226, "dur": 386, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097799, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097802, "dur": 68, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097873, "dur": 2, "ph": "X", "name": "ProcessMessages 1852", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599097876, "dur": 919, "ph": "X", "name": "ReadAsync 1852", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599098800, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599098804, "dur": 506, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599099437, "dur": 5, "ph": "X", "name": "ProcessMessages 2420", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599099444, "dur": 149, "ph": "X", "name": "ReadAsync 2420", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599099595, "dur": 6, "ph": "X", "name": "ProcessMessages 6956", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599099602, "dur": 600, "ph": "X", "name": "ReadAsync 6956", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100275, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100281, "dur": 144, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100436, "dur": 5, "ph": "X", "name": "ProcessMessages 2265", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100445, "dur": 75, "ph": "X", "name": "ReadAsync 2265", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100532, "dur": 2, "ph": "X", "name": "ProcessMessages 1431", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100535, "dur": 150, "ph": "X", "name": "ReadAsync 1431", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100700, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100702, "dur": 42, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100799, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599100803, "dur": 279, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101085, "dur": 2, "ph": "X", "name": "ProcessMessages 1853", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101088, "dur": 323, "ph": "X", "name": "ReadAsync 1853", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101446, "dur": 4, "ph": "X", "name": "ProcessMessages 3166", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101452, "dur": 54, "ph": "X", "name": "ReadAsync 3166", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101509, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101514, "dur": 45, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101563, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101571, "dur": 61, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101636, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101638, "dur": 328, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101973, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599101976, "dur": 84, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102065, "dur": 3, "ph": "X", "name": "ProcessMessages 3197", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102071, "dur": 384, "ph": "X", "name": "ReadAsync 3197", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102467, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102470, "dur": 66, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102566, "dur": 2, "ph": "X", "name": "ProcessMessages 1505", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102570, "dur": 60, "ph": "X", "name": "ReadAsync 1505", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102633, "dur": 2, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102637, "dur": 34, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102726, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599102729, "dur": 89, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599107925, "dur": 5, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599107945, "dur": 776, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599108727, "dur": 24, "ph": "X", "name": "ProcessMessages 20509", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599108801, "dur": 462, "ph": "X", "name": "ReadAsync 20509", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109267, "dur": 10, "ph": "X", "name": "ProcessMessages 9492", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109278, "dur": 100, "ph": "X", "name": "ReadAsync 9492", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109394, "dur": 4, "ph": "X", "name": "ProcessMessages 2859", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109399, "dur": 73, "ph": "X", "name": "ReadAsync 2859", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109476, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109479, "dur": 448, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109932, "dur": 6, "ph": "X", "name": "ProcessMessages 4709", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599109945, "dur": 69, "ph": "X", "name": "ReadAsync 4709", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110030, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110033, "dur": 558, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110596, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110598, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110671, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110674, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110715, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110717, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110760, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110792, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110795, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110835, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110865, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110866, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110902, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110930, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110932, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110959, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599110962, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111001, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111003, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111365, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111411, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111415, "dur": 42, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111464, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111494, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111497, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111781, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599111783, "dur": 437, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599112225, "dur": 28, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599112255, "dur": 461, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599112801, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599112806, "dur": 105, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599112913, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599112917, "dur": 323, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113244, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113247, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113432, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113437, "dur": 148, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113625, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113627, "dur": 210, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113912, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599113914, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599114132, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599114135, "dur": 934, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599115406, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599115410, "dur": 126, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599115564, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599115733, "dur": 258, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116190, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116196, "dur": 184, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116484, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116488, "dur": 110, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116777, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116781, "dur": 83, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116889, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599116891, "dur": 105, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599117210, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599117214, "dur": 3460, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599120682, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599120688, "dur": 72, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599120764, "dur": 4, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599120770, "dur": 12645, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133424, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133429, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133468, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133470, "dur": 144, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133621, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133652, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133656, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599133982, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599134009, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599134011, "dur": 1556, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599135574, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599135576, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599135645, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599135647, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599135693, "dur": 503, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136200, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136203, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136240, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136242, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136286, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136346, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136348, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136453, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136498, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599136501, "dur": 639, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137146, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137221, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137223, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137333, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137335, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137383, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137385, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137535, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137537, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137569, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137612, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137614, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137640, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599137668, "dur": 360, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138031, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138033, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138247, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138272, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138310, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138333, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138365, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138386, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138407, "dur": 313, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138723, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599138726, "dur": 944, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139674, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139676, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139716, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139719, "dur": 25, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139747, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139832, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139854, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139906, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139930, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599139982, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140012, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140014, "dur": 414, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140433, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140435, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140633, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140636, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140708, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140710, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140950, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140953, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599140999, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141001, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141099, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141128, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141165, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141213, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141218, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141259, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141261, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141339, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141379, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141381, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141685, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141726, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141727, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141755, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141758, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141808, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141834, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599141836, "dur": 802, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142642, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142646, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142705, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142707, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142746, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142776, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142853, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142855, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142894, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599142896, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599143000, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599143038, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599143187, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599143189, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599143230, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599143232, "dur": 392555, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599535803, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599535808, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599535923, "dur": 327, "ph": "X", "name": "ProcessMessages 3069", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599536254, "dur": 2948, "ph": "X", "name": "ReadAsync 3069", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599539210, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599539214, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599539282, "dur": 31, "ph": "X", "name": "ProcessMessages 3060", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599539315, "dur": 18456, "ph": "X", "name": "ReadAsync 3060", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599557786, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599557789, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599557828, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14000, "tid": 107374182400, "ts": 1750357599557832, "dur": 9232, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14000, "tid": 105, "ts": 1750357599579317, "dur": 913, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14000, "tid": 103079215104, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14000, "tid": 103079215104, "ts": 1750357599052570, "dur": 139725, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14000, "tid": 103079215104, "ts": 1750357599192298, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14000, "tid": 103079215104, "ts": 1750357599192300, "dur": 76, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14000, "tid": 105, "ts": 1750357599580234, "dur": 12, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14000, "tid": 98784247808, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14000, "tid": 98784247808, "ts": 1750357599030146, "dur": 537796, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14000, "tid": 98784247808, "ts": 1750357599031596, "dur": 14116, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14000, "tid": 98784247808, "ts": 1750357599568446, "dur": 2079, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14000, "tid": 98784247808, "ts": 1750357599568838, "dur": 1227, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14000, "tid": 98784247808, "ts": 1750357599570527, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14000, "tid": 105, "ts": 1750357599580248, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750357599086716, "dur": 93, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599086855, "dur": 3345, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599090216, "dur": 606, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599090917, "dur": 399, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599091530, "dur": 304, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599092112, "dur": 878, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D9B7D3234F369077.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599093347, "dur": 816, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9283034F04690CF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599094169, "dur": 349, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_A36FF8077F8E7B00.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599094524, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4C2401B10833EC61.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599094591, "dur": 172, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_60DA01DF63CFD649.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599094861, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B800F4FB36E290D5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599095046, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9A8E98434D7100DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599095128, "dur": 350, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6648D96BDBBB29C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599095637, "dur": 365, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599096007, "dur": 335, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D0F1A61EB4189FC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599096524, "dur": 600, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599097130, "dur": 376, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599097529, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AC3B8AED076F4CEB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599097886, "dur": 502, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C62338721BDB794D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599098489, "dur": 390, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599099010, "dur": 363, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_28E7CDA6A9657C3E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599099378, "dur": 385, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2E76C9AC399D6AFC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599100142, "dur": 444, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357599100632, "dur": 177, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357599101132, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357599101708, "dur": 349, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357599102471, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357599102653, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357599102748, "dur": 286, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357599103406, "dur": 320, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599103732, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357599104528, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599105406, "dur": 1035, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357599106633, "dur": 2328, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357599109490, "dur": 206, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357599109967, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357599110489, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357599091342, "dur": 19250, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599110607, "dur": 436213, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599546821, "dur": 269, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599558385, "dur": 2092, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750357599091783, "dur": 18878, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599110673, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2E76C9AC399D6AFC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599111084, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599111269, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599111329, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599111479, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599111627, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599112025, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D23AD71C5DDFE1B4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599112085, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599112212, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599112350, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_C33E9E8B76E14557.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599112681, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5F9D4CEA3FE42FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599112867, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599112985, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599113051, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1A4CC6E2C0ED3512.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599113134, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599113239, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599113765, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599114385, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599114450, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_BFE30F898F3B693C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599114502, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599114925, "dur": 833, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_BFE30F898F3B693C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599115820, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750357599115872, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599115969, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599116043, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599116114, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599116261, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599116333, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599116419, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599116472, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750357599116571, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599116665, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750357599117011, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599117082, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599117180, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599117273, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599117507, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750357599117904, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599118010, "dur": 538, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750357599118592, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599118807, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599118889, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599118976, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599120635, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599121181, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599121727, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599122913, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599123616, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599124357, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599125505, "dur": 1026, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\double3x4.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750357599126531, "dur": 1460, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\double3x3.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750357599128870, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\bool3.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750357599125339, "dur": 4086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599129425, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599130609, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599131798, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599132905, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599133656, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599134623, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599135744, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599136118, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599136881, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599137812, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599137956, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599138152, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599138257, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599138916, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599138997, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599139068, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599139201, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599139288, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599139999, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750357599140276, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599140341, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599140512, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599140750, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599141429, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599142042, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599142145, "dur": 1260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599143414, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599143651, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599143809, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357599143884, "dur": 398089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357599541974, "dur": 4832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599091730, "dur": 18895, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599110647, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_AE7AE761007ACD6C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599111133, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599111280, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599111343, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599111483, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599111590, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C62338721BDB794D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599111779, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599111887, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5E1525907236878A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599111941, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599112609, "dur": 1029, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5E1525907236878A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599113716, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599113918, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599114016, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599114175, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599114328, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599114603, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599114815, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599114905, "dur": 823, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599115729, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_85E4A9D67E927DC7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599115853, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599116235, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599116367, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599116443, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599116550, "dur": 17351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750357599133903, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599134041, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599134159, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599134256, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750357599134347, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599135520, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599136150, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599136870, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599137818, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599137954, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599138128, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599138221, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750357599138687, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599138962, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599139080, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599139202, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750357599139774, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599140083, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599140249, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599140602, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599140687, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599140826, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599140903, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750357599141545, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599141981, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599142100, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599142192, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357599142333, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599142487, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599143328, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599143588, "dur": 49449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599228776, "dur": 1033, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1750357599193039, "dur": 36776, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599229816, "dur": 312159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357599541975, "dur": 4851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599091766, "dur": 18881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599110656, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_5C4DA00CC016DBF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599111065, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599111239, "dur": 459, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_5C4DA00CC016DBF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599111700, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8F9DC40C34B6425C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599111831, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599111925, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599112306, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AC3B8AED076F4CEB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599112700, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599112787, "dur": 903, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599113732, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599114197, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_E90840FC7BA4DF8A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599114276, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599114539, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599114633, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599114717, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599114900, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599115138, "dur": 648, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599115907, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599116021, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599116166, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599116489, "dur": 520, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750357599117012, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599117169, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599117540, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750357599117954, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599118057, "dur": 469, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750357599118639, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599119078, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599119894, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599121338, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599122613, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599123408, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599123878, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599125729, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\uint4x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750357599124387, "dur": 2322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599127710, "dur": 1296, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Runtime\\TMP\\TextMeshProUGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750357599126710, "dur": 2533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599129243, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599130299, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599131634, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599132595, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599134343, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599134629, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599134964, "dur": 1167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599136131, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599136885, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599137838, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599137950, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599138123, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599138233, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750357599138780, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599138963, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599139080, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357599139209, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599139309, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599139977, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599140306, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599140500, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750357599140992, "dur": 274, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357599143940, "dur": 395905, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599091865, "dur": 18811, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599110686, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_28E7CDA6A9657C3E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599111776, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599112039, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599112453, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6C05DAAF3985B5DA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599112783, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599112937, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599113380, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599113453, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_47DDA486F86DF89C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599113559, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599113680, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_585E665D2A6D4152.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599113738, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599113873, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D9B7D3234F369077.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599114061, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599114660, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E3D9DEC668D47C33.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599114758, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599114986, "dur": 785, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E3D9DEC668D47C33.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599115792, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599116210, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599116368, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599116771, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599117131, "dur": 18743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599135875, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599136160, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599136297, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599136756, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599136913, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599137059, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599137369, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599137812, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599137953, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599138144, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599138252, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599138831, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599139222, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599139301, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599139486, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599139578, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599140104, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599140575, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599140638, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599140801, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599140870, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599141547, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599142018, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599142188, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599142335, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599142463, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750357599143133, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599143300, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599143398, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357599143555, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599143642, "dur": 86186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599229828, "dur": 312143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357599541971, "dur": 4826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599091913, "dur": 18774, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599110697, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3A585D315D7CDFE1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599111107, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599111245, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3A585D315D7CDFE1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599111616, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0399CE90B9A515B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599111791, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599111918, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599112008, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599112108, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599112371, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D0F1A61EB4189FC9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599112693, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_C412A4EF09BA29D5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599113013, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599113157, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599113324, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599113477, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_4FF2CB3C17FB8E73.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599113537, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599113986, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599114111, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599114252, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599114336, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_D461DBDF1BF3D038.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599114394, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599114507, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599114652, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599114776, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599115415, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599115527, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599115598, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599115662, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599115722, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_7000EBAFC0872D26.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599116010, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599116133, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599116570, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750357599116752, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599116829, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750357599117264, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599117415, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599117485, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599117645, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750357599117904, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599118008, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599118447, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750357599118595, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599118680, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599118768, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599119312, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\Testing\\TestAdaptor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750357599118909, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599120710, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599121711, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599123542, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines@b909627b5095\\Editor\\Controls\\SplineMeshHandle.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750357599122691, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599124777, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\rigid_transform.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750357599125754, "dur": 851, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\Noise\\common.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750357599124577, "dur": 2217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599126794, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599127862, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599128999, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599129674, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599131047, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599132571, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750357599132443, "dur": 2129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599134572, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599135398, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599136113, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599136185, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599136869, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599136925, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599137810, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599137894, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599137955, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599138128, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599138189, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750357599138786, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599139101, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357599139213, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599139271, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750357599139803, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599140088, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599140162, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599140230, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599140295, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599140593, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599140671, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750357599140737, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599140936, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599141406, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599142114, "dur": 1712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599143833, "dur": 398150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357599541984, "dur": 4824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599092118, "dur": 18617, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599110741, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599111157, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599111331, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599111393, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599111493, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599111544, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599111626, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_81157A417001B5EA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599111776, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599112291, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_81157A417001B5EA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599112653, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599112707, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599112873, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599113055, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599113169, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599113273, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599113395, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599113512, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599113664, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1E51E4CC49144E2E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599113790, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2FD890D4BBB4CE26.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599114487, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599114906, "dur": 835, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AF1FC69261D1616D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599115749, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599115898, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599116024, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599116496, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750357599116657, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599116707, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750357599116986, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599117378, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750357599117488, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599117551, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750357599117611, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750357599118027, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599118121, "dur": 601, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750357599118886, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599120159, "dur": 2731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599122891, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599123879, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599124802, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599126419, "dur": 945, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Runtime\\TMP\\TMP_TextProcessingStack.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750357599127889, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Runtime\\TMP\\TMP_SpriteGlyph.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750357599125857, "dur": 2662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599128594, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Runtime\\UGUI\\UI\\Core\\RawImage.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750357599128520, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599129718, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599131233, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599133168, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750357599134436, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750357599132197, "dur": 3276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599135473, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599136113, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599136192, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599136870, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599136956, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599137810, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599137952, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599138152, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599138216, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357599138380, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357599138804, "dur": 1056, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599139875, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357599140323, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599140499, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357599140910, "dur": 107, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599536358, "dur": 70, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357599143308, "dur": 393225, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357599541969, "dur": 4826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599092155, "dur": 18616, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599110782, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599111275, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599111464, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599111817, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1BC2F9751197887F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599111876, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599111993, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599112154, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599112504, "dur": 667, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_908A7CF0F95B5EED.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599113300, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599113498, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599113722, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599113897, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599113981, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599114160, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599114251, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599114691, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599114780, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599114874, "dur": 835, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599115711, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A3EA7E1937C99F5B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599115899, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599115977, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599116094, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599116171, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750357599116518, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599116800, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750357599117205, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599117334, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599117638, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599117778, "dur": 802, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599118587, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599118657, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599118733, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599118858, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599118922, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599122769, "dur": 1319, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics.Editor\\PrimitiveVectorDrawer.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750357599120389, "dur": 3766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599124200, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager@41738c275190\\Editor\\ISettingsRepository.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750357599124156, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599126331, "dur": 1669, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Runtime\\TMP\\TMP_Sprite.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750357599128066, "dur": 624, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Runtime\\TMP\\TMP_Settings.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750357599126066, "dur": 3885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599129952, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599131162, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599133643, "dur": 831, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750357599132565, "dur": 2090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599134865, "dur": 1269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599136134, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599136868, "dur": 960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599137828, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599137949, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599138116, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599138308, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750357599138651, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599138945, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599139103, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357599139226, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599139339, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599139928, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750357599140629, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599140884, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599140949, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599141293, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599141419, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599142109, "dur": 1737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599143847, "dur": 398134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357599541981, "dur": 4857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599092200, "dur": 18588, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599110790, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599111273, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599111346, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599111443, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599111870, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599111924, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599112040, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599112119, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C44092216EEC6649.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599112186, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599112286, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599112381, "dur": 319, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599112748, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113129, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113251, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113351, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113449, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113551, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113657, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113766, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599113914, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599114001, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C7FDD92EE7195664.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599114091, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599114458, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_567619744B52B229.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599114920, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599115055, "dur": 725, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_567619744B52B229.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599115784, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750357599115841, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599115943, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599116291, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599116662, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599116754, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750357599117032, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599117138, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599117209, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750357599117337, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599117468, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750357599117815, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599117868, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599118335, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750357599118681, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599118881, "dur": 2279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599121160, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599122176, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599123127, "dur": 924, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Deprecated\\CinemachineLegacyCameraEvents.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750357599123061, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599124982, "dur": 642, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\matrix.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750357599126084, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\int4.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750357599126798, "dur": 961, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\int3x2.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750357599124771, "dur": 3254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599128025, "dur": 668, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Editor\\UGUI\\EventSystem\\InputModuleComponentFactory.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750357599128025, "dur": 1987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599130012, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599131422, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599132156, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599133213, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599134006, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599135089, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599136120, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599136880, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599137812, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599137905, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599138075, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599138178, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599138287, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599138919, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599139088, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750357599139302, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357599139474, "dur": 891, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599140369, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750357599140784, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599141329, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599141767, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599141987, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599142127, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599143471, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599143767, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750357599143834, "dur": 398148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357599541982, "dur": 4835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357599564264, "dur": 2929, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14000, "tid": 105, "ts": 1750357599581469, "dur": 566, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14000, "tid": 105, "ts": 1750357599582147, "dur": 30788, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14000, "tid": 105, "ts": 1750357599578656, "dur": 34511, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}