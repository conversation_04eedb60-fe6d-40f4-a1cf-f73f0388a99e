using UnityEngine;
using UnityEngine.UI;
using TacticalCombatSystem.Interfaces.Core;

namespace TacticalCombatSystem.Characters
{
    public class BattleCharacter : MonoBehaviour
    {
        [Header("References")]
        public ICharacter characterData;
        public Transform visualRoot;
        public Animator animator;
        public Renderer characterRenderer;
        
        [Head<PERSON>("UI References")]
        public Canvas worldSpaceCanvas;
        public Image healthBar;
        public Text nameText;
        public Text healthText;
        
        private MaterialPropertyBlock propertyBlock;
        private static readonly int HitTrigger = Animator.StringToHash("Hit");
        
        private void Awake()
        {
            if (characterRenderer != null)
            {
                propertyBlock = new MaterialPropertyBlock();
                characterRenderer.GetPropertyBlock(propertyBlock);
            }
        }
        
        public void Initialize(ICharacter data, bool isPlayerTeam)
        {
            characterData = data;

            // Set up visual appearance
            if (characterRenderer != null)
            {
                // Example: Set team color tint
                Color teamColor = isPlayerTeam ? Color.blue : Color.red;
                propertyBlock.SetColor("_Color", teamColor);
                characterRenderer.SetPropertyBlock(propertyBlock);
            }

            // Set up UI
            if (worldSpaceCanvas != null)
            {
                if (nameText != null) nameText.text = characterData.CharacterName;
                UpdateHealthUI();
            }
        }
        
        // Note: This class is for visual representation only
        // Actual damage handling should be done through the combat system
        public void ShowDamageVisual(int damage)
        {
            // Visual feedback
            if (animator != null) animator.SetTrigger(HitTrigger);

            // Update UI
            UpdateHealthUI();

            // Show damage numbers
            ShowDamageNumber(damage);
        }
        
        private void UpdateHealthUI()
        {
            if (healthBar != null && characterData != null)
            {
                // Note: ICharacter interface doesn't have current health
                // This would need to be handled by a separate health component
                // For now, we'll use MaxHealth as a placeholder
                float healthPercent = 1.0f; // Placeholder - needs proper health tracking
                healthBar.fillAmount = healthPercent;
                healthBar.color = Color.Lerp(Color.red, Color.green, healthPercent);
            }

            if (healthText != null && characterData != null)
            {
                healthText.text = $"{characterData.MaxHealth}/{characterData.MaxHealth}"; // Placeholder
            }
        }
        
        private void ShowDamageNumber(int damage)
        {
            // Create damage number popup - simplified for now
            // TODO: Implement proper damage number system with object pooling
            Debug.Log($"Damage: {damage}");

            // For now, just create a simple floating text effect
            // This should be replaced with proper UI damage numbers later
        }
        
        public void PlayAttackAnimation()
        {
            if (animator != null) animator.SetTrigger("Attack");
        }
        
        public void PlayDeathAnimation()
        {
            if (animator != null) animator.SetTrigger("Die");
            // Additional death effects can be added here
        }
    }
}
