using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Core;
using ICombatParticipant = TacticalCombatSystem.Interfaces.ICombatParticipant;
using ICombatAction = TacticalCombatSystem.Interfaces.ICombatAction;

namespace TacticalCombatSystem.Characters
{
    /// <summary>
    /// Base class for all characters in the game
    /// </summary>
    public class BaseCharacter : MonoBehaviour, ICombatParticipant
    {
        // ICombatParticipant implementation
        public string ParticipantName => characterData?.CharacterName ?? "Unknown";

        public int CurrentHP
        {
            get => currentHP;
            set => currentHP = Mathf.Clamp(value, 0, MaxHP);
        }

        public int MaxHP => characterData?.MaxHealth ?? 0;

        public int CurrentMP
        {
            get => currentMP;
            set => currentMP = Mathf.Clamp(value, 0, MaxMP);
        }

        public int MaxMP => characterData?.MaxMana ?? 0;

        public int Attack => characterData?.Attack ?? 0;
        public int Defense => characterData?.Defense ?? 0;
        public int MagicAttack => characterData?.MagicAttack ?? 0;
        public int MagicDefense => characterData?.MagicDefense ?? 0;
        public int Speed => characterData?.Speed ?? 0;
        public float CriticalChance => characterData?.CriticalChance ?? 0f;
        public float CriticalMultiplier => characterData?.CriticalMultiplier ?? 1.5f;
        public bool IsAlive => CurrentHP > 0;
        public bool IsPlayerControlled => isPlayerControlled;

        [Header("Character Data")]
        [SerializeField] private Character characterData;
        [SerializeField] private bool isPlayerControlled = false;

        // Runtime stats (initialized from characterData)
        private int currentHP;
        private int currentMP;
        private readonly Dictionary<StatType, float> statModifiers = new Dictionary<StatType, float>();

        // Status effects
        private readonly List<object> activeStatusEffects = new List<object>();
        private readonly Dictionary<Type, object> statusEffectLookup = new Dictionary<Type, object>();

        // Combat actions
        private readonly List<ICombatAction> availableActions = new List<ICombatAction>();

        // References
        private BattleCharacter battleCharacter;
        private Animator characterAnimator;

        // ICombatParticipant implementation
        public List<ICombatAction> GetAvailableActions()
        {
            return new List<ICombatAction>(availableActions);
        }

        public bool CanPerformAction(ICombatAction action)
        {
            if (action == null) return false;
            return action.CanBeUsedBy(this);
        }

        public void ApplyStatusEffect(object effect)
        {
            if (effect == null) return;

            var effectType = effect.GetType();
            if (statusEffectLookup.TryGetValue(effectType, out var existingEffect))
            {
                // Stack or refresh the existing effect if it's a StatusEffect
                if (existingEffect is StatusEffect statusEffect)
                {
                    // Refresh the status effect
                    statusEffect.Refresh();
                }
            }
            else
            {
                // Add new effect
                activeStatusEffects.Add(effect);
                statusEffectLookup[effectType] = effect;

                // Apply effect if it's a StatusEffect
                if (effect is StatusEffect statusEffect)
                {
                    // Initialize the status effect on this character
                    statusEffect.Initialize(this);
                }

                OnStatusEffectApplied(effect);
            }
        }



        public bool HasStatusEffect(string effectId)
        {
            return activeStatusEffects.Exists(e =>
            {
                if (e is StatusEffect statusEffect)
                    return statusEffect.statusName == effectId;
                return e.GetType().Name == effectId;
            });
        }

        public List<object> GetStatusEffects()
        {
            return new List<object>(activeStatusEffects);
        }

        public bool CanTarget(ICombatParticipant target)
        {
            if (target == null) return false;
            return target.IsAlive;
        }

        public List<ICombatParticipant> GetValidTargets(ICombatAction action)
        {
            var targets = new List<ICombatParticipant>();
            if (action == null) return targets;

            // Find the battle manager in the scene
            var battleManager = FindFirstObjectByType<MonoBehaviour>() as IBattleManager;
            if (battleManager != null)
            {
                targets.AddRange(battleManager.GetPlayerTeam().FindAll(c => c.IsAlive));
                targets.AddRange(battleManager.GetEnemyTeam().FindAll(c => c.IsAlive));
            }

            return targets.FindAll(t => action.IsValidTarget(this, t));
        }

        public void OnTurnStart()
        {
            // Process start of turn status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                if (effect is StatusEffect statusEffect)
                {
                    // Process turn start for status effect
                    statusEffect.OnTurnStartEffect();
                }
            }

            // Notify listeners
            TurnStarted?.Invoke();
        }

        public void OnTurnEnd()
        {
            // Process end of turn status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                if (effect is StatusEffect statusEffect)
                {
                    // Process turn end for status effect
                    // Duration is already decremented in turn start

                    // Remove expired effects
                    if (statusEffect.duration <= 0)
                    {
                        RemoveStatusEffect(effect);
                    }
                }
            }

            // Reduce cooldowns on abilities
            foreach (var action in availableActions)
            {
                if (action is CombatAction combatAction)
                {
                    combatAction.ReduceCooldown();
                }
            }

            // Notify listeners
            TurnEnded?.Invoke();
        }

        public void OnActionPerformed(ICombatAction action)
        {
            // Process action performed status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                // Status effects can react to actions being performed
                // This would need to be implemented in the StatusEffect class
            }

            // Notify listeners
            ActionPerformed?.Invoke(action);
        }

        public void OnDamageTaken(int amount, ICombatParticipant source)
        {
            if (amount <= 0) return;

            // Apply damage
            CurrentHP -= amount;

            // Process damage taken status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                // Status effects can modify damage taken
                // This would need to be implemented in the StatusEffect class
            }

            // Notify listeners
            DamageTaken?.Invoke(amount, source);

            // Check for death
            if (!IsAlive)
            {
                OnDeath();
            }
        }

        public void OnHealed(int amount, ICombatParticipant source)
        {
            if (amount <= 0) return;

            // Apply healing
            int oldHP = CurrentHP;
            CurrentHP += amount;
            int actualHeal = CurrentHP - oldHP;

            // Process healing received status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                // Status effects can modify healing received
                // This would need to be implemented in the StatusEffect class
            }

            // Notify listeners
            if (actualHeal > 0)
            {
                HealingReceived?.Invoke(actualHeal, source);
            }
        }

        public void OnStatusEffectApplied(object effect)
        {
            StatusEffectApplied?.Invoke(effect);
        }

        public void OnStatusEffectRemoved(object effect)
        {
            StatusEffectRemoved?.Invoke(effect);
        }

        private void OnDeath()
        {
            // Process death status effects
            foreach (var effect in activeStatusEffects.ToArray())
            {
                // Status effects can react to death
                // This would need to be implemented in the StatusEffect class
            }

            // Clear all status effects
            activeStatusEffects.Clear();
            statusEffectLookup.Clear();

            // Notify listeners
            Died?.Invoke();
        }

        // Events
        public event Action TurnStarted;
        public event Action TurnEnded;
        public event Action<ICombatAction> ActionPerformed;
        public event Action<int, ICombatParticipant> DamageTaken;
        public event Action<int, ICombatParticipant> HealingReceived;
        public event Action<object> StatusEffectApplied;
        public event Action<object> StatusEffectRemoved;
        public event Action Died;

        // ICombatParticipant implementation - Using the one defined above with expression-bodied members
        public Character CharacterData => characterData;
        public Animator CharacterAnimator => characterAnimator;

        // Additional Events for BaseCharacter
        public event System.Action<BaseCharacter> OnHealthChanged;
        public event System.Action<BaseCharacter> OnManaChanged;
        public event System.Action<BaseCharacter> OnStatusEffectAdded;

        private void Awake()
        {
            characterAnimator = GetComponentInChildren<Animator>();
            battleCharacter = GetComponent<BattleCharacter>();

            // Initialize from character data if available
            if (characterData != null)
            {
                InitializeFromCharacterData(characterData, isPlayerControlled);
            }
        }

        /// <summary>
        /// Initialize this character with data from a Character ScriptableObject
        /// </summary>
        public void InitializeFromCharacterData(Character data, bool isPlayerControlled)
        {
            if (data == null) return;

            characterData = data;
            this.isPlayerControlled = isPlayerControlled;

            // Initialize stats
            currentHP = data.MaxHealth;
            currentMP = data.MaxMana;

            // Set up animator if available
            if (characterAnimator != null && data.AnimatorController != null)
            {
                characterAnimator.runtimeAnimatorController = data.AnimatorController;
            }

            // Apply any starting status effects
            foreach (var effect in data.StartingStatusEffects)
            {
                if (effect != null)
                {
                    ApplyStatusEffect(effect);
                }
            }

            // Notify listeners
            OnHealthChanged?.Invoke(this);
            OnManaChanged?.Invoke(this);
        }

        private void SetCurrentHP(int value)
        {
            int newHP = Mathf.Clamp(value, 0, MaxHP);
            if (currentHP != newHP)
            {
                currentHP = newHP;
                OnHealthChanged?.Invoke(this);

                if (currentHP <= 0)
                {
                    // Handle character death
                    HandleDeath();
                }
            }
        }

        #region ICombatParticipant Implementation



        /// <summary>
        /// Gets the current value of a stat, including all modifiers
        /// </summary>
        public int GetStat(StatType statType)
        {
            if (characterData == null) return 0;

            float baseValue = 0;

            // Get base value from character data
            switch (statType)
            {
                case StatType.Health:
                    return currentHP;
                case StatType.MaxHealth:
                    baseValue = characterData.MaxHealth;
                    break;
                case StatType.Mana:
                    return currentMP;
                case StatType.MaxMana:
                    baseValue = characterData.MaxMana;
                    break;
                case StatType.PhysicalAttack:
                    baseValue = characterData.Attack;
                    break;
                case StatType.PhysicalDefense:
                    baseValue = characterData.Defense;
                    break;
                case StatType.MagicAttack:
                    baseValue = characterData.MagicAttack;
                    break;
                case StatType.MagicDefense:
                    baseValue = characterData.MagicDefense;
                    break;
                case StatType.Agility:
                    baseValue = characterData.Speed;
                    break;
                default:
                    Debug.LogWarning($"Unhandled stat type: {statType}");
                    return 0;
            }

            // Apply stat modifiers
            if (statModifiers.TryGetValue(statType, out float modifier))
            {
                baseValue += modifier;
            }

            // Ensure stat doesn't go below 1 (except for HP/MP which are handled separately)
            if (statType != StatType.Health && statType != StatType.Mana)
            {
                baseValue = Mathf.Max(1, baseValue);
            }

            return Mathf.FloorToInt(baseValue);
        }

        /// <summary>
        /// Adds a modifier to a stat
        /// </summary>
        public void AddStatModifier(StatType statType, float value)
        {
            if (statModifiers.ContainsKey(statType))
            {
                statModifiers[statType] += value;
            }
            else
            {
                statModifiers[statType] = value;
            }

            // Update any affected stats
            if (statType == StatType.MaxHealth)
            {
                // Adjust current HP proportionally
                float healthRatio = (float)currentHP / MaxHP;
                currentHP = Mathf.FloorToInt(GetStat(StatType.MaxHealth) * healthRatio);
                OnHealthChanged?.Invoke(this);
            }
        }

        /// <summary>
        /// Removes a modifier from a stat
        /// </summary>
        public void RemoveStatModifier(StatType statType, float value)
        {
            if (statModifiers.ContainsKey(statType))
            {
                statModifiers[statType] -= value;

                // Remove the entry if it's now zero
                if (Mathf.Approximately(statModifiers[statType], 0f))
                {
                    statModifiers.Remove(statType);
                }

                // Update any affected stats
                if (statType == StatType.MaxHealth)
                {
                    // Ensure current HP doesn't exceed new max
                    currentHP = Mathf.Min(currentHP, GetStat(StatType.MaxHealth));
                    OnHealthChanged?.Invoke(this);
                }
            }
        }

        public void RemoveStatusEffect(object effect)
        {
            if (effect == null) return;

            var effectType = effect.GetType();
            if (statusEffectLookup.ContainsKey(effectType))
            {
                activeStatusEffects.Remove(effect);
                statusEffectLookup.Remove(effectType);

                // Remove effect if it's a StatusEffect
                if (effect is StatusEffect statusEffect)
                {
                    statusEffect.RemoveEffect();
                }

                OnStatusEffectRemoved(effect);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Add an action to this character's available actions
        /// </summary>
        public void AddAction(ICombatAction action)
        {
            if (action != null && !availableActions.Contains(action))
            {
                availableActions.Add(action);
            }
        }

        /// <summary>
        /// Remove an action from this character's available actions
        /// </summary>
        public void RemoveAction(ICombatAction action)
        {
            if (action != null)
            {
                availableActions.Remove(action);
            }
        }

        #endregion

        #region Combat Actions

        /// <summary>
        /// Makes the character take damage, applying defense calculations
        /// </summary>
        public int TakeDamage(int baseDamage, bool isMagical = false)
        {
            if (!IsAlive) return 0;

            // Calculate damage after defense
            int defense = isMagical ? MagicDefense : Defense;
            int damage = Mathf.Max(1, baseDamage - defense);

            // Apply damage
            CurrentHP -= damage;

            // Show damage number if this is a battle character
            // Note: ShowDamageNumber method needs to be public in BattleCharacter
            // battleCharacter?.ShowDamageNumber(damage);

            // Trigger hit animation
            characterAnimator?.SetTrigger("Hit");

            Debug.Log($"{ParticipantName} took {damage} damage!");

            return damage;
        }

        /// <summary>
        /// Heals the character by the specified amount
        /// </summary>
        public int Heal(int amount)
        {
            if (!IsAlive) return 0;

            int healAmount = Mathf.Min(amount, MaxHP - CurrentHP);
            CurrentHP += healAmount;

            // Show heal number if this is a battle character
            // Note: ShowHealNumber method needs to be implemented in BattleCharacter
            // battleCharacter?.ShowHealNumber(healAmount);

            Debug.Log($"{ParticipantName} healed for {healAmount} HP!");

            return healAmount;
        }

        /// <summary>
        /// Spends mana points
        /// </summary>
        public bool SpendMana(int amount)
        {
            if (CurrentMP < amount) return false;

            CurrentMP -= amount;
            return true;
        }

        /// <summary>
        /// Restores mana points
        /// </summary>
        public int RestoreMana(int amount)
        {
            int restoreAmount = Mathf.Min(amount, MaxMP - CurrentMP);
            CurrentMP += restoreAmount;
            return restoreAmount;
        }

        #endregion

        #region Visual Feedback

        /// <summary>
        /// Highlight the character with the specified color
        /// </summary>
        public void Highlight(Color color)
        {
            // Find all renderers in this character and its children
            var renderers = GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                // Store original colors if not already stored
                if (!originalColors.ContainsKey(renderer))
                {
                    var materials = renderer.materials;
                    var colors = new Color[materials.Length];
                    for (int i = 0; i < materials.Length; i++)
                    {
                        colors[i] = materials[i].color;
                    }
                    originalColors[renderer] = colors;
                }

                // Apply highlight color
                var rendererMaterials = renderer.materials;
                for (int i = 0; i < rendererMaterials.Length; i++)
                {
                    rendererMaterials[i].color = Color.Lerp(originalColors[renderer][i], color, 0.5f);
                }
            }
        }

        /// <summary>
        /// Reset the character's highlight to original colors
        /// </summary>
        public void ResetHighlight()
        {
            // Restore original colors
            foreach (var kvp in originalColors)
            {
                var renderer = kvp.Key;
                var colors = kvp.Value;

                if (renderer != null)
                {
                    var materials = renderer.materials;
                    for (int i = 0; i < materials.Length && i < colors.Length; i++)
                    {
                        materials[i].color = colors[i];
                    }
                }
            }
        }

        // Dictionary to store original colors for highlighting
        private readonly Dictionary<Renderer, Color[]> originalColors = new();

        #endregion

        #region Private Methods

        private void HandleDeath()
        {
            // Trigger death animation
            characterAnimator?.SetTrigger("Die");

            // Notify listeners
            // TODO: Add OnDeath event

            Debug.Log($"{ParticipantName} has been defeated!");

            // Disable components that shouldn't be active when dead
            var collider = GetComponent<Collider>();
            if (collider != null) collider.enabled = false;

            // If this is a battle character, handle battle-specific death logic
            // Note: OnDeath method needs to be implemented in BattleCharacter
            // battleCharacter?.OnDeath();
        }

        #endregion
    }
}
